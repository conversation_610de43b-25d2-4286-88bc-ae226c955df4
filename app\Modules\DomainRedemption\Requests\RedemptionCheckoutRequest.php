<?php

namespace App\Modules\DomainRedemption\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Cart\Services\CheckoutCartService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\DomainRedemption\Services\DomainRedemptionService;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Rules\Payment\ValidateStripeFees;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RedemptionCheckoutRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return $this->getValidationRules();
    }

    public function prepareForValidation()
    {
        $this->merge(['payment_summary_type' => PaymentSummaryType::PAYMENT_INVOICE]);
    }

    public function passedValidation()
    {
        if ($this->payment_service_type === PaymentServiceType::ACCOUNT_CREDIT) {
            CheckoutCartService::instance()->checkAccountCreditBalance($this->amount_to_use);
            $this->cancelIntent();
        }

        $this->captureIntent();
    }

    protected function failedValidation(Validator $validator)
    {
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new FailedRequestException(404, $validator->errors()->first(), 'Page not found');
    }

    public function processRedemption(): string
    {
        return DomainRedemptionService::instance()->processRedemption($this->all());
    }

    private function captureIntent()
    {
        if ($this->payment_service_type === PaymentServiceType::STRIPE) {
            PaymentIntentProvider::instance()->captureIntent($this->intent);
        }
    }

    private function cancelIntent()
    {
        if ($this->intent) {
            PaymentIntentProvider::instance()->cancelIntent($this->intent);
        }
    }

    private function getValidationRules(): array
    {
        $rules = [
            'domains' => ['required', 'array', 'min:1', 'max:1'],
            'other_fees' => ['required', 'array', 'min:1'],
            'payment_service_type' => ['required', Rule::in([PaymentServiceType::STRIPE, PaymentServiceType::ACCOUNT_CREDIT])],
            'payment_summary_type' => ['required', Rule::in(PaymentSummaryType::ALL)],
        ];

        return match ($this->payment_service_type) {
            PaymentServiceType::STRIPE => array_merge(
                $rules,
                [
                    'intent' => ['required', 'string'],
                    'stripe_fees' => ['required', 'array', 'min:1', new ValidateStripeFees],
                ]
            ),
            PaymentServiceType::ACCOUNT_CREDIT => array_merge($rules, ['amount_to_use' => ['required', 'numeric']]),
            default => $rules
        };
    }
}
