<?php

namespace App\Modules\DomainRedemption\Services\Payloads;

class RestoreReportPayload
{
    public string $domainName;
    public string $email;
    public array $eppInfoData;

    public function __construct(string $domainName, string $email, array $eppInfoData)
    {
        $this->domainName = $domainName;
        $this->email = $email;
        $this->eppInfoData = $eppInfoData;
    }

    public function toArray(): array
    {
        $data = $this->eppInfoData['data'] ?? [];
        
        return [
            'name' => $this->domainName,
            'report' => [
                'deleted' => $data['updated'] ?? now()->toISOString(),
                'restored' => now()->toISOString(),
                'preData' => [
                    'name' => $data['name'] ?? $this->domainName,
                    'registrant' => $data['registrant'] ?? null,
                    'email' => $this->email,
                    'status' => $data['status'] ?? [],
                    'nameservers' => $data['nameservers'] ?? [],
                    'created' => $data['created'] ?? null,
                    'expired' => $data['expiry'] ?? null
                ],
                'postData' => [
                    'name' => $data['name'] ?? $this->domainName,
                    'registrant' => $data['registrant'] ?? null,
                    'email' => $this->email,
                    'status' => $data['status'] ?? [],
                    'nameservers' => $data['nameservers'] ?? [],
                    'created' => $data['created'] ?? null,
                    'expired' => $this->getExtendedExpiryDate($data['expiry'] ?? null)
                ],
                'reason' => 'Domain restored due to registrar error',
                'statement1' => 'The domain was deleted unintentionally.',
                'statement2' => 'All issues have been resolved.',
                'other' => 'No further issues reported.'
            ]
        ];
    }

    private function getExtendedExpiryDate(?string $originalExpiry): ?string
    {
        if (!$originalExpiry) {
            return null;
        }

        try {
            $date = new \DateTime($originalExpiry);
            $date->add(new \DateInterval('P1Y'));
            return $date->format('Y-m-d\TH:i:s\Z');
        } catch (\Exception $e) {
            return $originalExpiry;
        }
    }


}
