import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import SearchNameFilter from "@/Components/Util/Filter/SearchNameFilter";
import { get } from "lodash";

export default function SearchDomainFilter({
    hasDomainSearch = false,
    setHasDomainSearch,
    setDomainSearchValue,
    searchedDomain = ""
}) {
    const excludedFilters = ['domain']

    const getOtherParam = () => {
        return Object.fromEntries(
            Object.entries(route().params).filter(([key]) => !excludedFilters.includes(key))
        );
    }

    const handleRemoveSearch = () => {
        setHasDomainSearch(false);
        setDomainSearchValue('');

        let payload = getOtherParam();
        router.get(route(route().current(), payload));
    };

    const handleSearch = (e) => {
        const value = e.target.value;
        setDomainSearchValue(value);
    };

    const handleSubmitSearch = () => {
        const searchTerm = searchedDomain.trim();

        if (!searchTerm) {
            toast.error("Please enter a domain to search");
            return;
        }

        let payload = getOtherParam()
        payload.domain = searchTerm

        router.get(route(route().current(), payload));
        setHasDomainSearch(true);
        toast.info("Searching domain, please wait...");
    };

    const handleOutsideClick = () => {
        if (searchedDomain.trim() && !hasDomainSearch) {
            handleSubmitSearch();
        }
    };

    return (
        <SearchNameFilter
            value={searchedDomain}
            onChange={handleSearch}
            onClear={handleRemoveSearch}
            onSubmit={handleSubmitSearch}
            onOutsideClick={handleOutsideClick}
            hasSearch={hasDomainSearch}
            placeholder="Search domain..."
        />
    );
} 