<?php

namespace App\Modules\DomainCancellationRequest\Requests;

// use App\Modules\DomainEppSearch\Services\SearchDomainService;
use App\Modules\DomainCancellationRequest\Services\DomainCancellationService;
use App\Rules\ValidFormat;
use App\Rules\TwoFactorAuthenticatorAppCodeVerificationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class CancellationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    public function rules(): array
    {
        return [
            'domain' => ['required', 'string', 'exists:domains,name'],
            'reason' => ['required', 'string', 'min:10'],
            'agree_policy' => ['accepted'],
            'agree_grace' => ['accepted'],
        ];

        
    }

    public function messages(){
        return [
            'domain.required' => 'The domain field is required.',
            'domain.exists' => 'The selected domain does not exist or is not valid.',
            'reason.required' => 'The reason field is required.',
            'reason.min' => 'Reason must be at least 10 characters.',
            'agree_policy.accepted' => 'You must agree to the cancellation policy.',
            'agree_grace.accepted' => 'You must acknowledge the 5-day grace period.',
        ];
    }

    public function calcellationRequest()
    {
        return DomainCancellationService::instance()->addCancellationRequest($this->all());
    }
    
}
