<?php

namespace App\Modules\DomainRedemption\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\DomainRedemption\Services\JobServices\JobRedemptionRecord;
use App\Modules\DomainRedemption\Services\JobServices\JobRedemptionSuccessService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\Domain\DomainParser;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Throwable;

class SuccessDomainRedemptionJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private array $params;

    public $failOnTimeout = true;

    public function __construct(object $domain, int $userId, array $refundDetails = [])
    {
        $this->params = [
            'domain' => $domain,
            'userId' => $userId,
            'domainName' => $domain->domain_name,
            'domainId' => $domain->domain_id,
            'refundDetails' => $refundDetails,
        ];

        $registry = DomainParser::getRegistryName($domain->domain_name);

        $this->onConnection(QueueConnection::DOMAIN_REDEMPTION);
        $this->onQueue(QueueTypes::DOMAIN_REDEMPTION[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return intval(now()->timestamp . $this->params['domainId']);
    }

    public function handle(): void
    {
        try {
            $jobRecord = new JobRedemptionRecord($this->params);
            JobRedemptionSuccessService::instance()->handle($jobRecord);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $this->getUserEmail()));
            if (strcmp($e->getMessage(), QueueErrorTypes::RETRY) === 0) {
                $this->retry();
                return;
            }

            $this->fail();
        }
    }

    public function failed(Throwable $exception): void
    {
        $jobRecord = new JobRedemptionRecord($this->params);
        $jobRecord->processRefund('Success Job Failed');
    }

    public function retry(): void
    {
        
    }

    private function getUserEmail(): string
    {
        $user = DB::table('users')->where('id', $this->params['userId'])->first();
        return $user->email;
    }
}