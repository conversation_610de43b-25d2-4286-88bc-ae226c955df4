<?php

use App\Modules\DomainRedemption\Controllers\DomainRedemptionController;
use Illuminate\Support\Facades\Route;

Route::middleware(
    [
        'auth',
        'auth.active',
        'account.setup'
    ]
)
    ->prefix('domain-redemption')
    ->group(
        function () {
            Route::get('/', [DomainRedemptionController::class, 'index'])->name('domain-redemption');
            Route::get('/{id}', [DomainRedemptionController::class, 'show'])->where('id', '[0-9]+')->name('domain-redemption.show');
            Route::post('/pay', [DomainRedemptionController::class, 'pay'])->name('domain-redemption.pay');
            Route::post('/validate', [DomainRedemptionController::class, 'validateRestore'])->name('domain-redemption.validate');
            Route::post('/restore', [DomainRedemptionController::class, 'restore'])->name('domain-redemption.restore');
        }
    );
