[2025-08-06 02:24:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-06 02:24:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-06 02:24:02] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-06 02:24:02] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-06 02:24:02] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-06 02:24:03] local.INFO: GeneralNotification: Running...  
[2025-08-06 02:24:03] local.INFO: GeneralNotification: done  
[2025-08-06 02:24:04] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-06 02:24:04] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-06 02:24:04] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-06 02:24:04] local.INFO: AfternicDomainTask: done  
[2025-08-06 02:24:04] local.INFO: AfternicJobRetry: Running...  
[2025-08-06 02:24:05] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-06 02:24:05] local.INFO: AfternicManualTransfer: Running...  
[2025-08-06 02:24:05] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-06 02:24:06] local.INFO: AccountCreditor: Running...  
[2025-08-06 02:24:06] local.INFO: AccountCreditor: Done  
[2025-08-06 02:24:07] local.ERROR: {"error":"ArgumentCountError","message":"Too few arguments to function App\\Modules\\Payment\\Services\\PaymentReimbursementService::refundByRegisteredDomain(), 6 passed in C:\\1xampp\\htdocs\\sd-client\\app\\Console\\Commands\\Domain\\ProcessDomainDeletionRefunds.php on line 45 and exactly 7 expected","url":"http:\/\/localhost\/strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\app\\Console\\Commands\\Domain\\ProcessDomainDeletionRefunds.php(45): App\\Modules\\Payment\\Services\\PaymentReimbursementService->refundByRegisteredDomain(116, 7, 'DOMAIN_REGISTRA...', 'EXPIRED', 'ACCOUNT_CREDIT', true)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\Domain\\ProcessDomainDeletionRefunds->handle()
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\Domain\\ProcessDomainDeletionRefunds), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 {main}"}  
[2025-08-06 02:24:07] local.ERROR: {"error":"Exception","message":"Scheduled command [\"C:\\1xampp\\php\\php.exe\" \"artisan\" domain:deletion-refund] failed with exit code [1].","url":"http:\/\/localhost\/strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Factory.php(59): Illuminate\\Console\\View\\Components\\Task->render('<fg=gray>2025-0...', Object(Closure))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Scheduling\\ScheduleRunCommand.php(187): Illuminate\\Console\\View\\Components\\Factory->__call('task', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Scheduling\\ScheduleRunCommand.php(132): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent(Object(Illuminate\\Console\\Scheduling\\Event))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle(Object(Illuminate\\Console\\Scheduling\\Schedule), Object(Illuminate\\Events\\Dispatcher), Object(Illuminate\\Cache\\Repository), Object(NunoMaduro\\Collision\\Adapters\\Laravel\\ExceptionHandler))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Console\\Scheduling\\ScheduleRunCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 {main}"}  
[2025-08-06 02:24:08] local.INFO: SessionPollChecker: Running...  
[2025-08-06 02:24:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-06 02:24:09] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-06 02:24:09] local.INFO: SessionPollChecker: Done  
[2025-08-06 02:25:01] local.INFO: GeneralNotification: Running...  
[2025-08-06 02:25:01] local.INFO: GeneralNotification: done  
[2025-08-06 02:25:01] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-06 02:25:01] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-06 02:25:01] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-06 02:25:01] local.INFO: AfternicDomainTask: done  
[2025-08-06 02:25:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-06 02:25:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-06 02:25:03] local.ERROR: {"error":"ArgumentCountError","message":"Too few arguments to function App\\Modules\\Payment\\Services\\PaymentReimbursementService::refundByRegisteredDomain(), 6 passed in C:\\1xampp\\htdocs\\sd-client\\app\\Console\\Commands\\Domain\\ProcessDomainDeletionRefunds.php on line 45 and exactly 7 expected","url":"http:\/\/localhost\/strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\app\\Console\\Commands\\Domain\\ProcessDomainDeletionRefunds.php(45): App\\Modules\\Payment\\Services\\PaymentReimbursementService->refundByRegisteredDomain(116, 7, 'DOMAIN_REGISTRA...', 'EXPIRED', 'ACCOUNT_CREDIT', true)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\Domain\\ProcessDomainDeletionRefunds->handle()
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\Domain\\ProcessDomainDeletionRefunds), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 {main}"}  
[2025-08-06 02:25:03] local.ERROR: {"error":"Exception","message":"Scheduled command [\"C:\\1xampp\\php\\php.exe\" \"artisan\" domain:deletion-refund] failed with exit code [1].","url":"http:\/\/localhost\/strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Factory.php(59): Illuminate\\Console\\View\\Components\\Task->render('<fg=gray>2025-0...', Object(Closure))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Scheduling\\ScheduleRunCommand.php(187): Illuminate\\Console\\View\\Components\\Factory->__call('task', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Scheduling\\ScheduleRunCommand.php(132): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent(Object(Illuminate\\Console\\Scheduling\\Event))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle(Object(Illuminate\\Console\\Scheduling\\Schedule), Object(Illuminate\\Events\\Dispatcher), Object(Illuminate\\Cache\\Repository), Object(NunoMaduro\\Collision\\Adapters\\Laravel\\ExceptionHandler))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Console\\Scheduling\\ScheduleRunCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 {main}"}  
