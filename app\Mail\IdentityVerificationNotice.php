<?php

namespace App\Mail;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Stripe\Constants\StripeIdentityEvent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class IdentityVerificationNotice extends Mailable implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $payload;

    private $userId;

    private $status;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    public $uniqueFor = 120; // 2 minutes

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;

    /**
     * The maximum number of unhandled exceptions to allow before failing.
     *
     * @var int
     */
    public $maxExceptions = 5;

    /**
     * Create a new message instance.
     */
    public function __construct(array $payload)
    {
        $this->status = $payload['status'];
        $this->userId = $payload['user_id'];
        $this->payload = $payload;

        app(AuthLogger::class)->info('IdentityVerificationNotice: '.$this->status);
    }

    public function uniqueId(): int
    {
        return $this->userId;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address($this->payload['sender_address'], $this->payload['sender_name']),
            subject: $this->payload['subject'],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $view = $this->getView();

        return new Content(
            markdown: $view,
            with: [
                'greeting' => $this->payload['greeting'],
                'sender_name' => $this->payload['sender_name'],
                'reason' => $this->payload['reason'],
                'redirectUrl' => $this->payload['redirectUrl'],
                'supportUrl' => $this->payload['supportUrl'],
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments() {}

    public function failed($e)
    {
        app(AuthLogger::class)->error('IdentityVerificationNotice: '.$e->getMessage());
        $this->fail();
    }

    private function getView()
    {
        switch ($this->status) {
            case StripeIdentityEvent::REQUIRES_INPUT:
                return 'Mails.IdentityVerification.FailedNotice';
            case StripeIdentityEvent::VERIFIED:
                return 'Mails.IdentityVerification.VerifiedNotice';
            case StripeIdentityEvent::PROCESSING:
            default:
                return 'Mails.IdentityVerification.ProcessingNotice';
        }
    }
}
