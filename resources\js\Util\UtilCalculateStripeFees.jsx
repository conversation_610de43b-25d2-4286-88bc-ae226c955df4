export default function UtilCalculateStripeFees(value, type='LOCAL')
{
    if (!value)
    {
        return 0;
    }

    const FIXED_FEE  = 0.30;                 // $0.30
    const percentFee = getPercentFee(type);
    
    return ((value * percentFee) + FIXED_FEE).toFixed(2);  
}

function getPercentFee(type='INTERNATIONAL')
{
    const LOCAL_CARD_FEE         = 0.029;  // 2.9%
    const INTERNATIONAL_CARD_FEE = 0.015;  // 1.5%
    const CONVERSION_FEE         = 0.01;   // 1%

    switch (type)
    {
        case 'INTERNATIONAL':
            return INTERNATIONAL_CARD_FEE;
        case 'LOCAL':
            return LOCAL_CARD_FEE;  
    }
}