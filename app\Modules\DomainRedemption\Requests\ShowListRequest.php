<?php

namespace App\Modules\DomainRedemption\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ShowListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'search' => ['nullable', 'string', 'max:255'],
            'status' => ['nullable', 'string', 'in:pending_delete,redemption_period,pending_restore,restored'],
        ];
    }

    public function getAllData(): array
    {
        return [
            'search' => $this->input('search'),
            'status' => $this->input('status'),
        ];
    }
}