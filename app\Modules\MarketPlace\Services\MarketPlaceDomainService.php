<?php

namespace App\Modules\MarketPlace\Services;

use App\Events\DomainHistoryEvent;
use App\Models\AfternicOfferHistory;
use App\Models\AfternicOffers;
use App\Models\MarketPlaceDomains;
use App\Models\MarketPlaceNodeInvoice;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\MarketPlace\Constants\AfternicOfferConstants;
use App\Modules\MarketPlace\Constants\MarketConstants;
use App\Modules\MarketPlace\Jobs\AfternicCreateOrder;
use App\Modules\MarketPlace\Mail\MarketOfferMail;
use App\Modules\Payment\Constants\PaymentFees;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Setting\Services\Settings;
use App\Util\Helper\Domain\DomainTld;
use App\Util\Helper\Store\BulkActions;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class MarketPlaceDomainService
{
    use UserLoggerTrait;

    private $icann_fee;

    public function __construct()
    {
        $this->icann_fee = floatval(Settings::instance()->getValueByKey(SettingKey::DOMAIN_ICANN_FEE));
    }

    public static function instance()
    {
        $marketPlaceDomainService = new self;

        return $marketPlaceDomainService;
    }

    public function store(array $data, string $type = FeeType::TRANSFER)
    {
        $otherFees = $data['other_fees'];
        $fees = $otherFees[PaymentFees::TYPE[$type]];
        $status = $data['status'] ?? MarketConstants::DOMAIN_PENDING_HOLD;
        // cast to payload
        $castedDomainContent = $this->castToPayload($data['registered_domains'], $fees, $data['user_id'], $status);
        // bulk insert payment nodes
        $this->bulkInsertMarketPlaceDomainNodes($castedDomainContent, $data['invoice']['id']);
    }

    public function placeAfternicPendingHold(array $params)
    {
        $id = $params['id'];
        $from = $params['from'];
        $domain = $params['domain'];
        $userId = $params['userId'];

        MarketPaymentService::instance()->placeHold($domain->name);

        if($from == 'market') {
            MarketDomainService::instance()->updateByRegisteredDomainId(
                $domain->id,
                $userId,
                ['status' => MarketConstants::DOMAIN_PENDING_ORDER]
            );
        } else if($from == 'offer') {
            $offer = AfternicOffers::where('id', $id)->first();
            $offer->update(['offer_status' => AfternicOfferConstants::PAID_ORDER_PENDING]);

            AfternicOfferHistory::create([
                'afternic_offer_id' => $id,
                'offer_price' => $offer->offer_price,
                'counter_offer_price' => $offer->counter_offer_price,
                'offer_status' => AfternicOfferConstants::PAID_ORDER_PENDING
            ]);

            $reg = MarketOfferService::instance()->getRegistrationID($offer->domain_name);

            MarketDomainService::instance()->updateByRegisteredDomainId(
                $reg->id,
                $userId,
                ['status' => MarketConstants::DOMAIN_PENDING_ORDER]
            );
        }

        event(new DomainHistoryEvent([
            'domain_id' => $params['domain']->domain_id,
            'type' => 'TRANSFER_PURCHASE_PENDING',
            'user_id' => $userId,
            'status' => 'success',
            'message' => 'Domain is successfully on hold with Afternic',
            'payload' => $params,

        ]));

        AfternicCreateOrder::dispatch($domain, $userId, $from, $id);
    }

    public function onErrorAfternicPendingHold(array $params)
    {
        $domain = $params['domain'];
        $userId = $params['userId'];

        $marketDomain = MarketDomainService::instance()->getByRegisteredDomainId($domain->id, $userId);
        AfternicReimbursementService::instance()->refundFailedDomain($marketDomain);
    }

    public function createOrderFromPendingDomain(array $params)
    {
        $id = $params['id'];
        $from = $params['from'];
        $domain = $params['domain'];
        $userId = $params['userId'];

        $orderId = MarketPaymentService::instance()->getOrderID($domain->name, $domain->price);

        if($from == 'market') {    
            MarketDomainService::instance()->updateByRegisteredDomainId(
                $domain->id,
                $userId,
                ['status' => MarketConstants::DOMAIN_PENDING, 'order_id' => $orderId]
            );
        } else if($from == 'offer') {            
            $offer = AfternicOffers::where('id', $id)->first();
            $offer->update(['offer_status' => AfternicOfferConstants::PAID_TRANSFER_PENDING, 'order_id' => $orderId]);

            AfternicOfferHistory::create([
                'afternic_offer_id' => $id,
                'offer_price' => $offer->offer_price,
                'counter_offer_price' => $offer->counter_offer_price,
                'offer_status' => AfternicOfferConstants::PAID_TRANSFER_PENDING,
                'feedback' => 'Order ID: ' . $orderId
            ]);

            $reg = MarketOfferService::instance()->getRegistrationID($offer->domain_name);

            MarketDomainService::instance()->updateByRegisteredDomainId(
                $reg->id,
                $userId,
                ['status' => AfternicOfferConstants::PAID_TRANSFER_PENDING]
            );
        }

        event(new DomainHistoryEvent([
            'domain_id' => $params['domain']->domain_id,
            'type' => 'TRANSFER_PURCHASE_PENDING',
            'user_id' => $userId,
            'status' => 'success',
            'message' => 'Successfully created order ID: '.$orderId,
            'payload' => ['domain' => $domain, 'status' => MarketConstants::DOMAIN_PENDING, 'order_id' => $orderId],

        ]));
    }

    // PRIVATE FUNCTIONS

    private function castToPayload(Collection $domainContent, array $fee, int $userId, string $status)
    {
        $extensions = DomainTld::getAllExtensionsById();

        return $domainContent->map(function ($e) use ($fee, $extensions, $userId, $status) {
            $yearLength = $e->year_length ?? 1;
            $extension_fee = $this->getFeesbyExtension($e->extension_id, $fee, $extensions);
            $rate = $extension_fee['rate'];
            $price = $e->price ?? 0;
            $total_icann_fee = $yearLength * $this->icann_fee;
            $total_domain_amount = $yearLength * $rate;
            $total_amount = $price + $total_icann_fee + $total_domain_amount;
            // app(AuthLogger::class)->info($this->fromWho('market valid payload '.json_encode($e)));
            // app(AuthLogger::class)->info($this->fromWho('market $rate '.json_encode($rate)));
            // app(AuthLogger::class)->info($this->fromWho('market $extension_fee '.json_encode($extension_fee)));

            return [
                'user_id' => $userId,
                'registered_domain_id' => $e->id,
                'status' => $status,
                'order_id' => 'order_id',
                'total_amount' => $total_amount,
                'epp_error' => null,
                'price' => $price,
                'total_icann_fee' => $total_icann_fee,
                'total_domain_amount' => $total_domain_amount,
                'vendor' => $e->vendor ?? '',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),

            ];
        });
    }

    private function getFeesbyExtension(int $extension_id, array $fee, array $extensions): array
    {
        $ext_name = strtolower($extensions[$extension_id]->name) ?? '';

        return [
            'id' => $fee[$ext_name]['id'] ?? 0,
            'rate' => $fee[$ext_name]['price'] ?? 0,
        ];
    }

    private function bulkInsertMarketPlaceDomainNodes(Collection $domainContent, int $invoiceId)
    {
        $marketDomainTable = (new MarketPlaceDomains)->getTable();
        $createdNodes = BulkActions::instance()->bulkInsertGetData($marketDomainTable, $domainContent->toArray(), false);
        $createdNodeIds = $createdNodes->pluck('id')->toArray();
        $nodeInvoices = [];
        foreach ($createdNodeIds as $nodeId) {
            $nodeInvoice = [
                'marketplace_payment_node_id' => $nodeId,
                'marketplace_payment_invoice_id' => $invoiceId,
            ];
            $nodeInvoices[] = $nodeInvoice;
        }
        MarketPlaceNodeInvoice::insert($nodeInvoices);

        app(AuthLogger::class)->info($this->fromWho('Created market place domains for  '.count($createdNodeIds).' domains.'));
        app(AuthLogger::class)->info($this->fromWho('Created market payment node invoices for  '.count($nodeInvoices).' nodes.'));
    }
}
