<?php

namespace App\Modules\BankTransfer\Services;

use App\Models\BankTransfer;
use App\Modules\AccountCredit\Services\DepositAccountCreditService;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BankTransferService
{
    use CursorPaginate;

    private $pageLimit = 20;

    public static function instance(): self
    {
        $bankTransferService = new self;

        return $bankTransferService;
    }

    public function requestStore(array $request)
    {
        DepositAccountCreditService::instance()->store(
            $request, $this->getUserId(), PaymentServiceType::BANK_TRANSFER);
    }

    public function getIndexData($request)
    {
        $pageLimit = $request->input('limit', $this->pageLimit);
        $builder = $this->indexQuery($request)
            ->paginate($pageLimit)
            ->appends($this->paramToURI($request))
            ->withQueryString();

        return CursorPaginate::cursor($builder, $this->paramToURI($request));
    }

    public function getPaymentServiceView(object $paymentService)
    {
        $data = DB::table('payment_services')
            ->join('bank_transfers', 'bank_transfers.id', '=', 'payment_services.bank_transfer_id')
            ->join('payment_summaries', 'payment_summaries.payment_service_id', '=', 'payment_services.id')
            ->where('payment_services.id', $paymentService->id)
            ->where('payment_services.user_id', $paymentService->user_id)
            ->where('bank_transfers.id', $paymentService->bank_transfer_id)
            ->select(
                'payment_services.*',
                'bank_transfers.account_name',
                'bank_transfers.verified_at',
                'bank_transfers.gross_amount',
                'bank_transfers.net_amount',
                'bank_transfers.service_fee',
                'bank_transfers.company',
                'bank_transfers.note',
                'payment_summaries.paid_amount as paid_amount',
                'payment_summaries.total_amount as total_amount',
                'payment_summaries.name as summary_name',
                'payment_summaries.type as summary_type',
                'payment_summaries.transaction_id',
            )->get()->first();

        $data->payment_service_type = PaymentServiceType::BANK_TRANSFER;

        return $data;
    }

    public function getVerifiedTransferById(int $bankTransferId, int $userId)
    {
        return $this->queryActiveRequests('asc')
            ->where('bank_transfers.id', $bankTransferId)
            ->where('payment_services.user_id', $userId)
            ->where('bank_transfers.verified_at', '!=', null)
            ->where('bank_transfers.retrieved_at', '!=', null)
            ->where('bank_transfers.credited_at', '=', null)
            ->get()->first();
    }

    public function getBankTransferById(int $bankTransferId, int $userId, int $paymentServiceId)
    {
        return DB::table('bank_transfers')
            ->join('payment_services', 'payment_services.bank_transfer_id', '=', 'bank_transfers.id')
            ->join('users', 'users.id', '=', 'payment_services.user_id')
            ->select(
                'bank_transfers.*',
                'bank_transfers.verified_at as status',
                'bank_transfers.gross_amount as amount',
                'payment_services.id as payment_service_id',
                'payment_services.user_id',
                'users.first_name',
                'users.last_name',
                'users.email',
            )
            ->where('bank_transfers.id', $bankTransferId)
            ->where('payment_services.id', $paymentServiceId)
            ->where('payment_services.user_id', $userId)
            ->orderBy('bank_transfers.id', 'desc')
            ->whereNull('bank_transfers.deleted_at')
            ->get()->first();
    }

    // DB QUERIES

    public function store(array $request): BankTransfer
    {
        $data = [
            'user_id' => $request['user_id'] ?? $this->getUserId(),
            'account_name' => $request['name'] ?? '',
            'gross_amount' => $request['gross_amount'] ?? $request['amount'] ?? 0,
            'net_amount' => $request['net_amount'] ?? 0,
            'service_fee' => $request['service_fee'] ?? 0,
            'company' => $request['company'] ?? '',
            'note' => $request['note'] ?? null,
        ];

        return BankTransfer::create($data);
    }

    public function query()
    {
        return DB::table('bank_transfers')
            ->join('payment_services', 'payment_services.bank_transfer_id', '=', 'bank_transfers.id')
            ->join('users', 'users.id', '=', 'payment_services.user_id')
            ->whereNull('payment_services.account_credit_id')
            ->select(
                'bank_transfers.*',
                'bank_transfers.verified_at as status',
                'bank_transfers.gross_amount as amount',
                'payment_services.id as payment_service_id',
                'payment_services.user_id',
                'users.first_name',
                'users.last_name',
                'users.email',
            );
    }

    public function indexQuery($request = null)
    {
        $indexQuery = $this->query()->where('payment_services.user_id', $this->getUserId());

        if ($request) {
            $indexQuery = $this->applyFilter($indexQuery, $request);
        } else {
            $indexQuery->orderBy('bank_transfers.id', 'desc');
        }

        return $indexQuery;
    }

    private function applyFilter($query, $request)
    {
        if ($request->has('orderby')) {
            switch ($request->orderby) {
                case 'created_at:desc':
                    $query->orderBy('bank_transfers.created_at', 'desc');
                    break;
                case 'created_at:asc':
                    $query->orderBy('bank_transfers.created_at', 'asc');
                    break;
                case 'amount:desc':
                    $query->orderBy('bank_transfers.gross_amount', 'desc');
                    break;
                case 'amount:asc':
                    $query->orderBy('bank_transfers.gross_amount', 'asc');
                    break;
                default:
                    $query->orderBy('bank_transfers.id', 'desc');
                    break;
            }
        } else {
            $query->orderBy('bank_transfers.id', 'desc');
        }

        if ($request->has('status')) {
            switch ($request->status) {
                case 'verified':
                    $query->whereNotNull('bank_transfers.verified_at');
                    break;
                case 'unverified':
                    $query->whereNotNull('bank_transfers.reviewed_at');
                    break;
                case 'rejected':
                    $query->whereNotNull('bank_transfers.deleted_at');
                    break;
            }
        }

        return $query;
    }

    public function queryActiveRequests(string $orderby = 'desc')
    {
        return $this->query()
            ->orderBy('bank_transfers.id', $orderby)
            ->whereNull('bank_transfers.deleted_at');
    }

    public function getVerifiedBankTransfersToBeDebited(int $size = 10)
    {
        return $this->queryActiveRequests('asc')
            ->whereNotNull('bank_transfers.verified_at')
            ->whereNull('bank_transfers.retrieved_at')
            ->whereNull('bank_transfers.credited_at')
            ->limit($size)
            ->get();
    }

    public function getRejectedTransfers(int $size = 10)
    {
        return $this->query()
            ->orderBy('bank_transfers.id', 'asc')
            ->whereNotNull('bank_transfers.deleted_at')
            ->whereNull('bank_transfers.retrieved_at')
            ->limit($size)
            ->get();
    }

    public function getReviewedTransfers(int $size = 10)
    {
        return $this->queryActiveRequests('asc')
            ->whereNotNull('bank_transfers.reviewed_at')
            ->whereNull('bank_transfers.retrieved_at')
            ->limit($size)
            ->get();
    }

    public function getPendingBankTransfers()
    {
        return $this->queryActiveRequests('desc')
            ->whereNull('bank_transfers.credited_at')
            ->where('payment_services.user_id', $this->getUserId())
            ->get()->toArray();
    }

    public function updateCreditedTransfer($id)
    {
        DB::table('bank_transfers')
            ->when(is_int($id), function ($query) use ($id) {
                return $query->where('id', $id);
            })
            ->when(is_array($id), function ($query) use ($id) {
                return $query->whereIn('id', $id);
            })
            ->update(['credited_at' => now()]);
    }

    public function updateRetrievedTransfer(int|array $id)
    {
        DB::table('bank_transfers')
            ->when(is_int($id), function ($query) use ($id) {
                return $query->where('id', $id);
            })
            ->when(is_array($id), function ($query) use ($id) {
                return $query->whereIn('id', $id);
            })
            ->update(['retrieved_at' => now()]);
    }

    // PRIVATE FUNCTIONS

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function paramToURI($request)
    {
        $param = [];

        if ($request->has('orderby')) {
            $param[] = 'orderby='.$request->orderby;
        }

        if ($request->has('status')) {
            $param[] = 'status='.$request->status;
        }

        return $param;
    }
}
