<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RedemptionPaymentService extends Model
{
    use HasFactory;

    protected $fillable = [
        'redemption_order_id',
        'payment_service_id',
    ];

    public function redemptionOrder(): BelongsTo
    {
        return $this->belongsTo(RedemptionOrder::class);
    }

    public function paymentService(): BelongsTo
    {
        return $this->belongsTo(PaymentService::class);
    }
}
