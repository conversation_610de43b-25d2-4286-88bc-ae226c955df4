<?php

use App\Modules\Cart\Controllers\CheckoutController;
use App\Modules\Domain\Controllers\DomainController;
use App\Modules\Domain\Controllers\LockController;
use App\Modules\Domain\Controllers\RenewalController;
use App\Modules\Domain\Controllers\ViewController;
use App\Modules\UserDomainExport\Controllers\UserDomainExportController;
use App\Modules\Domain\Controllers\DomainPrivacyController;
use Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests;
use Illuminate\Support\Facades\Route;

Route::middleware(
    [
        'auth',
        'auth.active',
        'account.setup'
    ]
)
    ->prefix('domain')
    ->group(
        function () {
            Route::get('/', [ViewController::class, 'get'])->name('domain');
            Route::get('/products', [ViewController::class, 'products'])->name('domains.products');
            Route::post('/', [ViewController::class, 'searchDomains'])->name('search.domain');
            Route::post('/clear-searched-domains', [ViewController::class, 'clearSearchedDomains'])->name('search.domain.clear');

            Route::patch('/', [DomainController::class, 'update'])->name('domain.update');
            Route::get('/view/{id}', [ViewController::class, 'view'])->name('domain.view');
            Route::get('/edit/{id}', [ViewController::class, 'edit'])->name('domain.edit');
            Route::post('/request-authcode', [DomainController::class, 'requestAuthcode'])->name('domain.request-authcode');

            Route::prefix('renew')->group(function () {
                Route::post('/', [RenewalController::class, 'renew'])->name('domain.renew');
                Route::post('/confirm', [RenewalController::class, 'confirm'])->name('domain.renew.confirm');
                Route::post('/pay', [RenewalController::class, 'pay'])->name('domain.renew.pay');
            });

            Route::prefix('lock')->group(function () {
                Route::post('/', [LockController::class, 'lock'])
                    ->middleware([HandlePrecognitiveRequests::class])
                    ->name('domain.lock');
                Route::post('/confirm', [LockController::class, 'lockConfirm'])->name('domain.lock.confirm');
                Route::post('/auth-check', [LockController::class, 'checkAuthLock'])
                    ->middleware([HandlePrecognitiveRequests::class])
                    ->name('domain.lock.auth-check');
            });

            Route::prefix('payment')->group(function () {
                Route::get('/fail', [DomainController::class, 'paymentFailed'])->name('domain.payment.fail');
                Route::post('/error', [DomainController::class, 'limitStripeError'])->name('domain.payment.error');
            });

            Route::prefix('nameserver')->group(function () {
                Route::post('/select', [DomainController::class, 'selectNameservers'])->name('domain.select-nameserver');
                Route::post('/update', [DomainController::class, 'updateNameservers'])->middleware([HandlePrecognitiveRequests::class])->name('domain.update-nameserver');
                Route::post('/check', [DomainController::class, 'checkNameserver'])->name('domain.check-nameserver');
            });

            Route::prefix('privacy')->group(function () {
                Route::post('/{domain}', [DomainPrivacyController::class, 'updatePrivacy'])->name('domain.privacy.toggle');
            });


            //! USER DOMAIN EXPORT ROUTES
            Route::prefix('export')
                ->group(
                    function () {
                        Route::get('', [UserDomainExportController::class, 'index'])->name('domain.export.index');
                        Route::post('generate-all', [UserDomainExportController::class, 'generateAll'])->name('domain.export.generate.all');
                        Route::post('generate-selected', [UserDomainExportController::class, 'generateSelected'])->name('domain.export.generate.selected');
                        Route::get('{id}/download', [UserDomainExportController::class, 'download'])->name('domain.export.download');
                        Route::delete('{id}/delete', [UserDomainExportController::class, 'delete'])->name('domain.export.delete');
                        Route::delete('clear', [UserDomainExportController::class, 'clear'])->name('domain.export.clear');
                    }
                );

            // Route::post('/payment-option', [CheckoutController::class, 'paymentOption'])->name('domain.payment-option');
        }
    );
