<?php

namespace App\Modules\MarketPlace\Jobs;

use App\Events\DomainHistoryEvent;
use App\Models\MarketPlaceDomains;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\MarketPlace\Constants\MarketConstants;
use App\Modules\MarketPlace\Services\AfternicMiddleware;
use App\Modules\Transfer\Services\JobTransferService;
use App\Modules\Transfer\Services\TransferDataQueryService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\Domain\DomainParser;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AfternicUpdateTransferFromPoll implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    /**
     * Create a new job instance.
     */
    public function __construct($pollId, $status, $name)
    {
        $this->params = ['pollId' => $pollId, 'status' => $status, 'name' => $name];
        $registry = DomainParser::getRegistryName($name);

        $this->onConnection(QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE);
        $this->onQueue(QueueTypes::DOMAIN_TRANSFER_POLL_UPDATE[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return $this->params['pollId'];
    }

    public function handle(): void
    {
        app(AuthLogger::class)->info('AfternicTransferUpdate for domain: '.$this->params['name']);

        try {
            switch ($this->params['status']) {
                case EppDomainStatus::TRANSFER_CLIENT_APPROVED:
                case EppDomainStatus::TRANSFER_SERVER_APPROVED:
                    $this->handleServerApproved();
                    break;
                case EppDomainStatus::TRANSFER_CLIENT_REJECTED:
                    $this->handleRejected();
                    break;
            }
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), 'Cron:'));

            if (strcmp($e->getMessage(), QueueErrorTypes::RETRY) === 0) {
                $this->retry();

                return;
            }

            $this->fail();
        }
    }

    private function handleServerApproved(): void
    {
        $id = $this->getRecord();

        MarketPlaceDomains::where('registered_domain_id', $id[0]->registered_domain_id)->update(['status' => MarketConstants::DOMAIN_COMPLETED]);

        AfternicMiddleware::instance()::sendCompleted($id[0]->order_id);
        app(AuthLogger::class)->info('AfternicTransferUpdate: Domain Transfer success for domain '.$this->params['name']);

        // event(new DomainHistoryEvent([
        //     'domain_id' => $id[0]->domain_id ?? null,
        //     'type' => 'TRANSFER_PURCHASE_COMPLETED',
        //     'user_id' => Auth::id() ?? null,
        //     'status' => 'success',
        // ]));
    }

    private function handleRejected(): void
    {
        $id = $this->getRecord();

        MarketPlaceDomains::where('order_id', $id[0]->order_id)->update(['epp_error' => MarketConstants::STATUS_REJECTED]);
        AfternicMiddleware::instance()::sendCompletionFailureOther($id[0]->order_id);
    }

    private function getRecord()
    {
        return DB::table('domains')
            ->join('registered_domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('market_place_domains', 'registered_domains.domain_id', '=', 'market_place_domains.registered_domain_id')
            ->where('domains.name', $this->params['name'])
            ->select('registered_domains.id as registered_domain_id', 'market_place_domains.order_id')
            ->get();
    }

    /**
     * Execute the job.
     */
    public function retry(): void
    {
        $status = DomainStatus::PENDING;
        $type = QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE;
        $jobId = TransferDataQueryService::instance()->getDomainIdByDomainName($this->params['name']);

        // add to afternic job retry
        // JobTransferService::instance()->addRetryLogs($type, $status, $this->params, $jobId);
    }
}
