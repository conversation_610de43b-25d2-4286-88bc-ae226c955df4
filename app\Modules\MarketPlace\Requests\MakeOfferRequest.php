<?php

namespace App\Modules\MarketPlace\Requests;

use App\Models\AfternicOffers;
use Illuminate\Support\Facades\Auth;
use App\Models\AfternicOfferHistory;
use App\Modules\MarketPlace\Mail\MarketOfferMail;
use App\Modules\MarketPlace\Services\MarketOfferService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Mail;

class MakeOfferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'domain' => 'required|string',
            'offer' => 'required|integer|min:100'
        ];
    }

    public function sendOffer() : void
    {
        MarketOfferService::instance()->sendEmailOffer($this->domain, $this->offer);
    }
}
