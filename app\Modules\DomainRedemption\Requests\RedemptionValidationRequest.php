<?php

namespace App\Modules\DomainRedemption\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\DomainRedemption\Services\DomainRedemptionService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class RedemptionValidationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'domain_id' => ['required', 'exists:redemption_orders,id'],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new FailedRequestException(404, $validator->errors()->first(), 'Page not found');
    }

    public function validateDomainRestore(): array
    {
        $domain = DomainRedemptionService::instance()->getDomainById($this->domain_id);

        if (!$domain) {
            throw new FailedRequestException(404, 'Domain not found', 'Page not found');
        }

        if (empty($domain->domain_name)) {
            throw new FailedRequestException(422, 'Invalid domain name', 'Domain validation failed');
        }

        return [
            'success' => true,
            'domain' => $domain,
            'message' => 'Domain validation successful'
        ];
    }
}
