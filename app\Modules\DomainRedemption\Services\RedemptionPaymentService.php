<?php

namespace App\Modules\DomainRedemption\Services;

use App\Models\RedemptionPaymentService as RedemptionPaymentServiceModel;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Payment\Services\PaymentInvoiceService;
use App\Modules\PaymentService\Services\PaymentServiceHelper;
use App\Modules\Payment\Services\PaymentFeeService;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use Exception;
use Illuminate\Support\Facades\DB;

class RedemptionPaymentService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        return new self;
    }

    public function createRedemptionPayment(int $redemptionOrderId, array $paymentData, int $userId, string $paymentServiceType): array
    {
        $registeredDomain = $this->getRegisteredDomainByRedemptionOrderId($redemptionOrderId);
        $paymentPayload = $this->createRedemptionPaymentPayload($paymentData, $userId, $paymentServiceType, $registeredDomain);

        $summaryId = PaymentSummaryService::instance()->createPayment(
            $paymentPayload,
            $userId,
            PaymentSummaryType::PAYMENT_INVOICE,
            $paymentServiceType
        );

        $paymentSummary = PaymentSummaryService::instance()->getById($summaryId, $userId);
        $paymentInvoice = $this->getPaymentInvoiceById($paymentSummary->payment_invoice_id, $userId);
        $paymentService = PaymentServiceHelper::instance()->getPaymentServiceById($paymentInvoice->payment_service_id, $userId);

        $redemptionPaymentService = RedemptionPaymentServiceModel::create([
            'redemption_order_id' => $redemptionOrderId,
            'payment_service_id' => $paymentService->id,
        ]);

        app(AuthLogger::class)->info($this->fromWho("Redemption payment service created: Order {$redemptionOrderId}, Payment Service {$paymentService->id}", $this->getUserEmail($userId)));

        return [
            'redemption_payment_service' => $redemptionPaymentService,
            'payment_service' => $paymentService,
            'payment_data' => null,
            'payment_summary_id' => $summaryId,
        ];
    }

    public function getRedemptionPaymentService(int $redemptionOrderId): ?object
    {
        return DB::table('redemption_payment_services')
            ->join('payment_services', 'payment_services.id', '=', 'redemption_payment_services.payment_service_id')
            ->where('redemption_payment_services.redemption_order_id', $redemptionOrderId)
            ->select([
                'redemption_payment_services.*',
                'payment_services.user_id',
                'payment_services.stripe_id',
                'payment_services.account_credit_id',
                'payment_services.bank_transfer_id',
                'payment_services.system_credit_id',
                'payment_services.created_at as payment_created_at'
            ])
            ->first();
    }

    public function getUserRedemptionPayments(int $userId): array
    {
        return DB::table('redemption_payment_services')
            ->join('payment_services', 'payment_services.id', '=', 'redemption_payment_services.payment_service_id')
            ->join('redemption_orders', 'redemption_orders.id', '=', 'redemption_payment_services.redemption_order_id')
            ->join('domains', 'domains.id', '=', 'redemption_orders.domain_id')
            ->where('payment_services.user_id', $userId)
            ->select([
                'redemption_payment_services.id',
                'redemption_orders.total_amount',
                'redemption_orders.paid_at',
                'domains.name as domain_name',
                'payment_services.stripe_id',
                'payment_services.account_credit_id',
                'payment_services.bank_transfer_id',
                'payment_services.system_credit_id',
                'redemption_payment_services.created_at'
            ])
            ->orderBy('redemption_payment_services.created_at', 'desc')
            ->get()
            ->toArray();
    }

    public function processRedemptionRefund(int $redemptionOrderId, string $reason = 'Redemption Failed'): bool
    {
        $redemptionPayment = $this->getRedemptionPaymentService($redemptionOrderId);
        
        if (!$redemptionPayment) {
            app(AuthLogger::class)->error($this->fromWho("No payment service found for redemption order: {$redemptionOrderId}", 'System'));
            return false;
        }

        try {
            $paymentServiceType = $this->determinePaymentServiceType($redemptionPayment);
            
            $refundData = [
                'payment_service_id' => $redemptionPayment->payment_service_id,
                'reason' => $reason,
            ];

            PaymentServiceHelper::instance()->refund($refundData, $redemptionPayment->user_id, $paymentServiceType);

            app(AuthLogger::class)->info($this->fromWho("Redemption payment refund processed: Order {$redemptionOrderId}", $this->getUserEmail($redemptionPayment->user_id)));
            
            return true;

        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho("Failed to process redemption refund: {$e->getMessage()}", $this->getUserEmail($redemptionPayment->user_id)));
            return false;
        }
    }

    private function determinePaymentServiceType(object $paymentService): string
    {
        if ($paymentService->stripe_id) return 'STRIPE';
        if ($paymentService->account_credit_id) return 'ACCOUNT_CREDIT';
        if ($paymentService->bank_transfer_id) return 'BANK_TRANSFER';
        if ($paymentService->system_credit_id) return 'SYSTEM_CREDIT';
        
        return 'STRIPE'; // Default fallback
    }

    private function getUserEmail(int $userId): string
    {
        $user = DB::table('users')->where('id', $userId)->first();
        return $user->email ?? 'Unknown';
    }


    private function getPaymentSummaryByInvoiceId(int $paymentInvoiceId, int $userId): ?object
    {
        return DB::table('payment_summaries')
            ->where('payment_invoice_id', $paymentInvoiceId)
            ->where('user_id', $userId)
            ->first();
    }

    private function createRedemptionPaymentPayload(array $paymentData, int $userId, string $paymentServiceType, object $registeredDomain): array
    {
        $redemptionFee = $registeredDomain->redemption_fee; 
        $icannFee = PaymentFeeService::getIcannFee();
        $billTotal = $paymentData['gross_amount'] ?? 0;

        $otherFees = [
            'redemption_fee' => $redemptionFee,
            'redemption_total' => $redemptionFee,
            'icann_fee' => $icannFee,
            'bill_total' => $billTotal,
            'domain_count' => 1,
            'year_sum' => 1,
        ];

        $invoice = PaymentInvoiceService::instance()->createInvoicePayload(
            FeeType::REDEMPTION,
            $userId,
            $otherFees,
            $paymentData['payment_intent'] ?? null
        );

        $nodeInvoice = PaymentInvoiceService::instance()->createNodeInvoicePayload(
            FeeType::REDEMPTION,
            collect([$registeredDomain]),
            $otherFees
        );

        return PaymentInvoiceService::instance()->createPaymentPayload($invoice, $nodeInvoice);
    }

    private function getPaymentInvoiceById(int $invoiceId, int $userId): object
    {
        return DB::table('payment_invoices')
            ->join('payment_services', 'payment_services.id', '=', 'payment_invoices.payment_service_id')
            ->where('payment_invoices.id', $invoiceId)
            ->where('payment_services.user_id', $userId)
            ->select('payment_invoices.*')
            ->first();
    }

    private function getRegisteredDomainByRedemptionOrderId(int $redemptionOrderId): object
    {
        return DB::table('redemption_orders')
            ->join('registered_domains', 'registered_domains.domain_id', '=', 'redemption_orders.domain_id')
            ->where('redemption_orders.id', $redemptionOrderId)
            ->select(
                'registered_domains.id as registered_domain_id',
                'registered_domains.domain_id',
                'registered_domains.extension_id',
                'registered_domains.user_contact_registrar_id',
                'registered_domains.status',
                'redemption_orders.total_amount as redemption_fee',
                DB::raw('1 as year_length') // test 1 yr defalt
            )
            ->first();
    }
}
