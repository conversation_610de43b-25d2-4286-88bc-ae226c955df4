<?php

namespace App\Modules\MarketPlace\Services;

use App\Models\AfternicOfferHistory;
use App\Models\AfternicOffers;
use App\Modules\AccountCredit\Services\AccountCreditService;
use App\Modules\Cart\Services\MultiCheckout\MultiCartService;
use App\Modules\Cart\Services\MultiCheckout\MultiCheckoutService;
use App\Modules\Client\Jobs\ScheduleDomainExpiryNotice;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\MarketPlace\Constants\AfternicOfferConstants;
use App\Modules\MarketPlace\Jobs\AfternicDomainHold;
use App\Modules\MarketPlace\Mail\MarketOfferMail;
use App\Modules\Payment\Services\PaymentFeeService;
use App\Modules\PaymentSummary\Services\MultiCheckoutPaymentService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Services\ExtensionFees;
use App\Modules\Stripe\Helpers\StripeFeeHelper;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Modules\Stripe\Services\StripeLimiter;
use App\Util\Helper\Domain\DomainTld;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class MarketOfferService
{
    private $dispatchDelayInSeconds = 180; // three minutes

    public static function instance()
    {
        $marketOfferService = new self;

        return $marketOfferService;
    }

    public static function getOffers() : Collection
    {
        return AfternicOffers::where('user_id', Auth::id())->orderBy('id', 'asc')->get();
    }

    public static function getpendingOffers() : Collection
    {
        return AfternicOffers::where('offer_status', AfternicOfferConstants::PAID_TRANSFER_PENDING)->orderBy('id', 'asc')->get();
    }

    public static function getOfferHistory($id) : Collection
    {
        return AfternicOfferHistory::where('afternic_offer_id', $id)->orderBy('id', 'desc')->get();
    }

    public static function sendEmailOffer($domain, $price) : void
    {
        $offer = AfternicOffers::create([
            'user_id' => Auth::id(),
            'domain_name' => $domain,
            'offer_price' => $price,
            'counter_offer_price' => $price,
        ]);

        AfternicOfferHistory::create([
            'afternic_offer_id' => $offer->id,
            'offer_price' => $price,
            'counter_offer_price' => $price
        ]);

        Mail::to(Env('TEST_BROKER_EMAIL'))->send(new MarketOfferMail(['domain' => $domain, 'price' => $price]));
    }

    public static function closeDeal($id) : void
    {
        $offer = AfternicOffers::where('id', $id)->first();
        $offer->update(['offer_status' => AfternicOfferConstants::CLOSED]);

        AfternicOfferHistory::create([
            'afternic_offer_id' => $id,
            'offer_price' => $offer->offer_price,
            'counter_offer_price' => $offer->counter_offer_price,
            'offer_status' => AfternicOfferConstants::CLOSED
        ]);
    }

    public static function counter($id, $price, $feedback) : void
    {
        $offer = AfternicOffers::where('id', $id)->first();
        $offer->update(['counter_offer_price' => $price, 'offer_status' => AfternicOfferConstants::USER_COUNTER]);

        AfternicOfferHistory::create([
            'afternic_offer_id' => $id,
            'offer_price' => $offer->offer_price,
            'counter_offer_price' => $price,
            'feedback' => $feedback,
            'offer_status' => AfternicOfferConstants::USER_COUNTER
        ]);
    }

    public function getCheckoutData($id)
    {
        $threshold = MultiCartService::instance()->getDomainThreshold();
        $DomainThresholdCount = MultiCartService::instance()->getRegistrationThresholdData();

        return ($DomainThresholdCount >= $threshold->threshold_limit)
            ? MultiCartService::instance()->displayThresholdNotif()
            : $this->getOfferSummary($id);
    }

    public function getOfferSummary($id)
    {
        $offers = $this->getMarketplaceCheckoutData($id);

        $otherFeesData = PaymentFeeService::getOtherMulticheckoutFees([], $offers);
        $stripeFeeObj = StripeFeeHelper::calculateTransactionFee($otherFeesData['bill_total'] ?? 0);
        $setupIntents = $this->setPaymentDetails($stripeFeeObj['gross_amount'] ?? $otherFeesData['bill_total']);
        $accountCredit = AccountCreditService::instance()->getLatestBlock($this->getUserId());

        $data['other_fees'] = $otherFeesData;
        $data['secret'] = $setupIntents->client_secret;
        $data['intent'] = $setupIntents->id;
        $data['promise'] = Config::get('stripe.publishable_key');
        $data['account_credit_balance'] = $accountCredit->running_balance ?? 0;
        $data['stripeFeeObj'] = $stripeFeeObj;

        $data['domains'] = [];
        $data['market_domains'] = $offers;

        return $data;
    }

    public function getRegistrationID($domain)
    {
        return DB::table('domains')
        ->join('registered_domains', 'registered_domains.domain_id', 'domains.id')
        ->where('domains.name', $domain)
        ->select('registered_domains.*')
        ->first();
    }

    public function getRegistrationData($domain)
    {
        return DB::table('domains')
        ->join('registered_domains', 'registered_domains.domain_id', 'domains.id')
        ->where('domains.name', $domain)
        ->select('registered_domains.*')
        ->get();
    }

    public function getMarketID($regID)
    {
        return DB::table('market_place_domains')
        ->where('market_place_domains.registered_domain_id', $regID)
        ->first();
    }

    public function getMarketplaceCheckoutData($id)
    {
        $marketCarts = $this->getOffer($id);

        $tld = DomainTld::getTldByName($marketCarts[0]->domain_name);
        $marketCarts[0]->tld_id = $tld->id;

        if (empty($marketCarts)) {
            return [];
        }

        $transfer_fees = ExtensionFees::instance()->getDefaultFeesbyType(FeeType::TRANSFER);

        $other_fees = $this->getOtherFees($marketCarts, $transfer_fees);

        $this->checkRegistryBalance($marketCarts, $other_fees, FeeType::TRANSFER);

        $other_fees['price_total'] = $marketCarts[0]->counter_offer_price;
        $other_fees['premium_total'] = $other_fees['price_total'] + $other_fees['transfer_total'];
        $other_fees['bill_total'] = $other_fees['price_total'] + $other_fees['bill_total'];

        return [
            'domains' => $marketCarts,
            'other_fees' => $other_fees,
            'settings' => ['transfer_fees' => $transfer_fees],
            'user_id' => $this->getUserId(),
        ];
    }

    public function store($request)
    {
        StripeLimiter::instance()->clearAttempt();

        $request = $this->appendMissingFields($request);

        $market_data = collect([(object) $request['market_domains']['domains'][0]]);

        $marketDomains = $this->createMarketPlaceDomains($market_data);

        $summaryId = MultiCheckoutPaymentService::instance()->createPaymentSummary($request, null, $marketDomains);

        if ($marketDomains) {
            $this->setPaidPendingTransfer($market_data[0]->id);
            $this->callMarketRequestJob($marketDomains, $market_data[0]->id);
        }

        ScheduleDomainExpiryNotice::dispatch($this->getUserId())->delay($this->dispatchDelayInSeconds);

        return $summaryId ?? 0;
    }

    private function createMarketPlaceDomains($cartContent)
    {
        $createdDomains = MultiCheckoutService::instance()->getCreatedDomains($cartContent, UserDomainStatus::RESERVED);

        return $createdDomains;
    }

    private function setPaidPendingTransfer($id)
    {
        $offer = AfternicOffers::where('id', $id)->first();
        $offer->update(['offer_status' => AfternicOfferConstants::PAID_HOLD_PENDING]);

        AfternicOfferHistory::create([
            'afternic_offer_id' => $id,
            'offer_price' => $offer->offer_price,
            'counter_offer_price' => $offer->counter_offer_price,
            'offer_status' => AfternicOfferConstants::PAID_HOLD_PENDING
        ]);
    }

    private function appendMissingFields($request)
    {
        $request['market_domains']['domains'][0]['is_available'] = 1;
        $request['market_domains']['domains'][0]['vendor'] = 'afternic';
        $request['market_domains']['domains'][0]['is_fast_transfer'] = 0;
        $request['market_domains']['domains'][0]['name'] = $request['market_domains']['domains'][0]['domain_name'];
        $request['market_domains']['domains'][0]['price'] = $request['market_domains']['domains'][0]['counter_offer_price'];

        return $request;
    }

    private function callMarketRequestJob($marketDomains, $id)
    {
        foreach ($marketDomains as $domain) {
            AfternicDomainHold::dispatch($domain, $this->getUserId(), 'offer', $id);
        }
    }

    private function getOffer($id)
    {
        $query = DB::table('afternic_offers')
        ->where('id', $id)
        ->where('offer_status', AfternicOfferConstants::ACCEPTED)
        ->get()
        ->all();

        return $query ?? [];
    }

    private function checkRegistryBalance(array $domains, array $fees, string $type): array
    {
        return RegistryAccountBalanceService::checkRegistryBalance($domains, $fees, $type);
    }

    private function getOtherFees(array $domains, array $fees)
    {
        return PaymentFeeService::getOtherTransferFees($domains, $fees);
    }

    private function setPaymentDetails(float $bill_total): object
    {
        $payment = PaymentIntentProvider::instance()->createPaymentDetails($bill_total);

        return PaymentIntentProvider::instance()->create($payment);
    }

    private static function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }
}
