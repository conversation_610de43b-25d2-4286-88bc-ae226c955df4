<?php

namespace App\Console\Commands\Afternic;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\MarketPlace\Services\AfternicAPI;
use App\Modules\MarketPlace\Services\AfternicMiddleware;
use App\Modules\MarketPlace\Services\AfternicReimbursementService;
use App\Modules\MarketPlace\Services\DomainTransferService;
use App\Modules\MarketPlace\Services\MarketDomainService;
use App\Modules\MarketPlace\Services\MarketOfferService;
use Exception;
use Illuminate\Console\Command;

class AfternicDomainTask extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:afternic-domain-task';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'AfternicDomainTask: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('AfternicDomainTask: Running... checking market');

        $pending = MarketDomainService::instance()->getAllPendingDomains();
        
        if($pending->isEmpty()) app(AuthLogger::class)->info('AfternicDomainTask: marketplace empty, nothing to process...checking offers');
        else self::process($pending, 'market');
        
        $pendingOffer = MarketOfferService::instance()->getpendingOffers();
        if($pendingOffer->isEmpty()) app(AuthLogger::class)->info('AfternicDomainTask: offer empty, nothing to process..');
        else self::process($pendingOffer, 'offer');

        app(AuthLogger::class)->info('AfternicDomainTask: done');
    }

    private function process($pending, $from)
    {
        app(AuthLogger::class)->info('AfternicDomainTask: found ' . $pending->count() . ", start checking $from order ids for auth codes");

        foreach ($pending as $domain) {
            $res = AfternicMiddleware::instance()::getOrder($domain, $from);

            if($res->orderStatus == "TRANSFER_READY" && isset($res->authCode)) {
                app(AuthLogger::class)->info("AfternicDomainTask: checking order_id: #" . $domain->order_id . ", auth_code: true");
                
                # start transfer
                DomainTransferService::instance()->callTransfer($res, $domain, $from);
            } else if($res->orderStatus == "CANCELLED") {
                app(AuthLogger::class)->info("AfternicDomainTask: order_id: #" . $domain->order_id . " failed, initiating refund process");

                # initiate refund
                if($from == 'market') AfternicReimbursementService::instance()->refundFailedDomain($domain);
            } else {
                app(AuthLogger::class)->info("AfternicDomainTask: checking order_id: #" . $domain->order_id . ", auth code: false");
            }
        }
    }
}
